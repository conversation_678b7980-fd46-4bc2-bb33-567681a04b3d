// import 'dart:ui';
// import 'package:room_eight/core/utils/app_exports.dart';
// import 'package:room_eight/widgets/custom_widget/custom_gradient_container.dart';

// class AddPersonalDetailScreen extends StatelessWidget {
//   const AddPersonalDetailScreen({super.key});

//   static Widget builder(BuildContext context) =>
//       const AddPersonalDetailScreen();

//   @override
//   Widget build(BuildContext context) {
//     return BlocBuilder<AuthBloc, AuthState>(
//       builder: (context, state) {
//         return Scaffold(
//           backgroundColor: Theme.of(context).customColors.fillColor,
//           body: Form(
//             key: state.addPersonalDetailFormKey,
//             child: SingleChildScrollView(
//               child: BackgroundImage(
//                 imagePath: Assets.images.pngs.other.pngLoginBg.path,
//                 child: ConstrainedBox(
//                   constraints: BoxConstraints(
//                     minHeight: MediaQuery.of(context).size.height,
//                   ),
//                   child: IntrinsicHeight(
//                     child: Column(
//                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       children: [
//                         _buildTopSection(context),
//                         _buildLoginForm(context, state),
//                       ],
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         );
//       },
//     );
//   }

//   Widget _buildTopSection(BuildContext context) {
//     return Padding(
//       padding: EdgeInsets.only(
//         left: 16.w,
//         right: 16.w,
//         top: 60.h,
//         bottom: 16.h,
//       ),
//       child: _buildTopBar(context),
//     );
//   }

//   Widget _buildTopBar(BuildContext context) {
//     final customColors = Theme.of(context).customColors;

//     return Row(
//       mainAxisAlignment: MainAxisAlignment.start,
//       children: [
//         CustomGradientContainer(
//           height: 36.w,
//           width: 36.w,
//           topColor: customColors.fillColor!.withAlpha((0.6 * 255).toInt()),
//           bottomColor: customColors.blackColor!.withAlpha((0.4 * 255).toInt()),
//           fillColor: customColors.fillColor!.withAlpha(75),
//           child: CustomImageView(
//             imagePath: Assets.images.svgs.icons.icBackArrow.path,
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildLoginForm(BuildContext context, AuthState state) {
//     return Flexible(
//       child: ClipRRect(
//         borderRadius: const BorderRadius.only(
//           topLeft: Radius.circular(20),
//           topRight: Radius.circular(20),
//         ),
//         child: BackdropFilter(
//           filter: ImageFilter.blur(sigmaX: 7, sigmaY: 7),
//           child: Container(
//             width: double.infinity,
//             constraints: BoxConstraints(
//               minHeight: MediaQuery.of(context).size.height * 0.6,
//             ),
//             decoration: BoxDecoration(
//               color: Theme.of(
//                 context,
//               ).customColors.fillColor?.withValues(alpha: 0.8),
//               borderRadius: const BorderRadius.only(
//                 topLeft: Radius.circular(20),
//                 topRight: Radius.circular(20),
//               ),
//             ),
//             child: Padding(
//               padding: EdgeInsets.all(16.w),
//               child: Column(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   _buildDragHandle(context),
//                   buildSizedBoxH(24.h),
//                   _buildFullNameField(context, state),
//                   buildSizedBoxH(16.h),
//                   _buildDateOfBirthField(context, state),

//                   buildSizedBoxH(24.h),
//                   const Spacer(),
//                   _buildSignInButton(context, state),
//                   buildSizedBoxH(16.h),
//                   _buildSignUpOption(context),
//                 ],
//               ),
//             ),
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildDragHandle(BuildContext context) {
//     return Column(
//       children: [
//         Text(
//           Lang.of(context).lbl_your_personal_details,
//           textAlign: TextAlign.center,
//           style: Theme.of(context).textTheme.labelLarge?.copyWith(
//             fontSize: 22.sp,
//             color: Theme.of(context).customColors.blackColor,
//             fontWeight: FontWeight.bold,
//           ),
//         ),
//         buildSizedBoxH(4.h),
//         Text(
//           Lang.of(context).lbl_your_personal_details_desc,
//           textAlign: TextAlign.center,
//           style: Theme.of(context).textTheme.labelLarge?.copyWith(
//             fontSize: 14.sp,
//             color: Theme.of(context).customColors.darkGreytextcolor,
//             fontWeight: FontWeight.w500,
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildFullNameField(BuildContext context, AuthState state) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           Lang.of(context).lbl_what_is_your_full_name,
//           style: Theme.of(context).textTheme.labelLarge?.copyWith(
//             fontSize: 16.sp,
//             color: Theme.of(context).customColors.blackColor,
//           ),
//         ),
//         buildSizedBoxH(4.h),
//         CustomTextInputField(
//           context: context,
//           type: InputType.text,
//           hintLabel: Lang.of(context).lbl_full_name,
//           controller: state.fullNameController,
//           focusNode: state.fullNameFocusNode,
//           textInputAction: TextInputAction.next,
//           validator: (value) => AppValidations.nameValidation(value, context),
//           onChanged: (value) =>
//               context.read<AuthBloc>().add(FullNameChanged(value)),
//         ),
//       ],
//     );
//   }

//   Widget _buildDateOfBirthField(BuildContext context, AuthState state) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           Lang.of(context).lbl_what_is_your_date_of_birth,
//           style: Theme.of(context).textTheme.labelLarge?.copyWith(
//             fontSize: 16.sp,
//             color: Theme.of(context).customColors.blackColor,
//           ),
//         ),
//         buildSizedBoxH(4.h),
//         CustomTextInputField(
//           context: context,
//           type: InputType.text,
//           readOnly: true,
//           hintLabel: "DD/MM/YYYY",
//           controller: state.dobController,
//           focusNode: state.dobFocusNode,
//           textInputAction: TextInputAction.next,
//           validator: (value) {
//             if (value == null || value.isEmpty) {
//               return 'Date of birth is required';
//             }
//             return null;
//           },
//           onTap: () async {
//             FocusScope.of(context).requestFocus(FocusNode());
//             final now = DateTime.now();

//             // Store the theme data before the async operation
//             final primaryColor =
//                 Theme.of(context).customColors.primaryColor ??
//                 Theme.of(context).primaryColor;

//             final picked = await showDatePicker(
//               context: context,
//               initialDate: now.subtract(const Duration(days: 365 * 18)),
//               firstDate: DateTime(1900),
//               lastDate: now,
//               builder: (context, child) {
//                 return Theme(
//                   data: Theme.of(context).copyWith(
//                     colorScheme: ColorScheme.light(primary: primaryColor),
//                   ),
//                   child: child!,
//                 );
//               },
//             );

//             // Check if the widget is still mounted before using context
//             if (picked != null && context.mounted) {
//               final formatted =
//                   "${picked.day.toString().padLeft(2, '0')}/${picked.month.toString().padLeft(2, '0')}/${picked.year}";
//               context.read<AuthBloc>().add(DateOfBirthChanged(formatted));
//             }
//           },
//         ),
//       ],
//     );
//   }

//   Widget _buildSignInButton(BuildContext context, AuthState state) {
//     return CustomElevatedButton(
//       isLoading: state.isloginLoading,
//       isDisabled: state.isloginLoading,
//       text: Lang.of(context).lbl_login,
//       buttonTextStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
//         color: Theme.of(context).customColors.fillColor,
//         fontSize: 18.0.sp,
//         fontWeight: FontWeight.w500,
//       ),
//       onPressed: () {
//         FocusManager.instance.primaryFocus?.unfocus();
//         if (state.addPersonalDetailFormKey.currentState?.validate() ?? false) {
//           context.read<AuthBloc>().add(LoginSubmitted());
//         }
//       },
//     );
//   }

//   Widget _buildSignUpOption(BuildContext context) {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.center,
//       children: [
//         Text(
//           "Don't have an account? ",
//           style: Theme.of(context).textTheme.bodyMedium?.copyWith(
//             color: Theme.of(context).customColors.blackColor,
//             fontSize: 14.sp,
//           ),
//         ),
//         TextButton(
//           onPressed: () {
//             ScaffoldMessenger.of(context).showSnackBar(
//               const SnackBar(content: Text('Navigate to Sign Up screen')),
//             );
//           },
//           style: TextButton.styleFrom(
//             padding: EdgeInsets.zero,
//             minimumSize: Size.zero,
//             tapTargetSize: MaterialTapTargetSize.shrinkWrap,
//           ),
//           child: Text(
//             'Sign Up',
//             style: Theme.of(context).textTheme.bodyMedium?.copyWith(
//               color: Theme.of(context).customColors.primaryColor,
//               fontSize: 14.sp,
//               fontWeight: FontWeight.w600,
//             ),
//           ),
//         ),
//       ],
//     );
//   }
// }
// // import 'dart:ui';
// // import 'package:room_eight/core/utils/app_exports.dart';
// // import 'package:room_eight/widgets/custom_widget/custom_gradient_container.dart';

// // class AddPersonalDetailScreen extends StatelessWidget {
// //   const AddPersonalDetailScreen({super.key});

// //   static Widget builder(BuildContext context) =>
// //       const AddPersonalDetailScreen();

// //   @override
// //   Widget build(BuildContext context) {
// //     return BlocBuilder<AuthBloc, AuthState>(
// //       builder: (context, state) {
// //         return Scaffold(
// //           backgroundColor: Theme.of(context).customColors.fillColor,
// //           body: Form(
// //             key: state.addPersonalDetailFormKey,
// //             child: SingleChildScrollView(
// //               child: BackgroundImage(
// //                 imagePath: Assets.images.pngs.other.pngLoginBg.path,
// //                 child: ConstrainedBox(
// //                   constraints: BoxConstraints(
// //                     minHeight: MediaQuery.of(context).size.height,
// //                   ),
// //                   child: IntrinsicHeight(
// //                     child: Column(
// //                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
// //                       children: [
// //                         _buildTopSection(context),
// //                         _buildLoginForm(context, state),
// //                       ],
// //                     ),
// //                   ),
// //                 ),
// //               ),
// //             ),
// //           ),
// //         );
// //       },
// //     );
// //   }

// //   Widget _buildTopSection(BuildContext context) {
// //     return Padding(
// //       padding: EdgeInsets.only(
// //         left: 16.w,
// //         right: 16.w,
// //         top: 60.h,
// //         bottom: 16.h,
// //       ),
// //       child: _buildTopBar(context),
// //     );
// //   }

// //   Widget _buildTopBar(BuildContext context) {
// //     final customColors = Theme.of(context).customColors;

// //     return Row(
// //       mainAxisAlignment: MainAxisAlignment.start,
// //       children: [
// //         CustomGradientContainer(
// //           height: 36.w,
// //           width: 36.w,
// //           topColor: customColors.fillColor!.withAlpha((0.6 * 255).toInt()),
// //           bottomColor: customColors.blackColor!.withAlpha((0.4 * 255).toInt()),
// //           fillColor: customColors.fillColor!.withAlpha(75),
// //           child: CustomImageView(
// //             imagePath: Assets.images.svgs.icons.icBackArrow.path,
// //           ),
// //         ),
// //       ],
// //     );
// //   }

// //   Widget _buildLoginForm(BuildContext context, AuthState state) {
// //     return Flexible(
// //       child: ClipRRect(
// //         borderRadius: const BorderRadius.only(
// //           topLeft: Radius.circular(20),
// //           topRight: Radius.circular(20),
// //         ),
// //         child: BackdropFilter(
// //           filter: ImageFilter.blur(sigmaX: 7, sigmaY: 7),
// //           child: Container(
// //             width: double.infinity,
// //             constraints: BoxConstraints(
// //               minHeight: MediaQuery.of(context).size.height * 0.6,
// //             ),
// //             decoration: BoxDecoration(
// //               color: Theme.of(
// //                 context,
// //               ).customColors.fillColor?.withValues(alpha: 0.8),
// //               borderRadius: const BorderRadius.only(
// //                 topLeft: Radius.circular(20),
// //                 topRight: Radius.circular(20),
// //               ),
// //             ),
// //             child: Padding(
// //               padding: EdgeInsets.all(16.w),
// //               child: Column(
// //                 mainAxisSize: MainAxisSize.min,
// //                 children: [
// //                   _buildDragHandle(context),
// //                   buildSizedBoxH(24.h),
// //                   _buildFullNameField(context, state),
// //                   buildSizedBoxH(16.h),
// //                   _buildDateOfBirthField(context, state),

// //                   buildSizedBoxH(24.h),
// //                   const Spacer(),
// //                   _buildSignInButton(context, state),
// //                   buildSizedBoxH(16.h),
// //                   _buildSignUpOption(context),
// //                 ],
// //               ),
// //             ),
// //           ),
// //         ),
// //       ),
// //     );
// //   }

// //   Widget _buildDragHandle(BuildContext context) {
// //     return Column(
// //       children: [
// //         Text(
// //           Lang.of(context).lbl_your_personal_details,
// //           textAlign: TextAlign.center,
// //           style: Theme.of(context).textTheme.labelLarge?.copyWith(
// //             fontSize: 22.sp,
// //             color: Theme.of(context).customColors.blackColor,
// //             fontWeight: FontWeight.bold,
// //           ),
// //         ),
// //         buildSizedBoxH(4.h),
// //         Text(
// //           Lang.of(context).lbl_your_personal_details_desc,
// //           textAlign: TextAlign.center,
// //           style: Theme.of(context).textTheme.labelLarge?.copyWith(
// //             fontSize: 14.sp,
// //             color: Theme.of(context).customColors.darkGreytextcolor,
// //             fontWeight: FontWeight.w500,
// //           ),
// //         ),
// //       ],
// //     );
// //   }

// //   Widget _buildFullNameField(BuildContext context, AuthState state) {
// //     return Column(
// //       crossAxisAlignment: CrossAxisAlignment.start,
// //       children: [
// //         Text(
// //           Lang.of(context).lbl_what_is_your_full_name,
// //           style: Theme.of(context).textTheme.labelLarge?.copyWith(
// //             fontSize: 16.sp,
// //             color: Theme.of(context).customColors.blackColor,
// //           ),
// //         ),
// //         buildSizedBoxH(4.h),
// //         CustomTextInputField(
// //           context: context,
// //           type: InputType.text,
// //           hintLabel: Lang.of(context).lbl_full_name,
// //           controller: state.fullNameController,
// //           focusNode: state.fullNameFocusNode,
// //           textInputAction: TextInputAction.next,
// //           validator: (value) => AppValidations.nameValidation(value, context),
// //           onChanged: (value) =>
// //               context.read<AuthBloc>().add(FullNameChanged(value)),
// //         ),
// //       ],
// //     );
// //   }

// //   Widget _buildDateOfBirthField(BuildContext context, AuthState state) {
// //     return Column(
// //       crossAxisAlignment: CrossAxisAlignment.start,
// //       children: [
// //         Text(
// //           Lang.of(context).lbl_what_is_your_date_of_birth,
// //           style: Theme.of(context).textTheme.labelLarge?.copyWith(
// //             fontSize: 16.sp,
// //             color: Theme.of(context).customColors.blackColor,
// //           ),
// //         ),
// //         buildSizedBoxH(4.h),
// //         CustomTextInputField(
// //           context: context,
// //           type: InputType.text,
// //           readOnly: true,
// //           hintLabel: "DD/MM/YYYY",
// //           controller: state.dobController,
// //           focusNode: state.dobFocusNode,
// //           textInputAction: TextInputAction.next,
// //           validator: (value) {
// //             if (value == null || value.isEmpty) {
// //               return 'Date of birth is required';
// //             }
// //             return null;
// //           },
// //           onTap: () async {
// //             FocusScope.of(context).requestFocus(FocusNode());
// //             final now = DateTime.now();
// //             final picked = await showDatePicker(
// //               context: context,
// //               initialDate: now.subtract(const Duration(days: 365 * 18)),
// //               firstDate: DateTime(1900),
// //               lastDate: now,
// //               builder: (context, child) {
// //                 return Theme(
// //                   data: Theme.of(context).copyWith(
// //                     colorScheme: ColorScheme.light(
// //                       primary:
// //                           Theme.of(context).customColors.primaryColor ??
// //                           Theme.of(context).primaryColor,
// //                     ),
// //                   ),
// //                   child: child!,
// //                 );
// //               },
// //             );
// //             if (picked != null) {
// //               final formatted =
// //                   "${picked.day.toString().padLeft(2, '0')}/${picked.month.toString().padLeft(2, '0')}/${picked.year}";
// //               context.read<AuthBloc>().add(DateOfBirthChanged(formatted));
// //             }
// //           },
// //         ),
// //       ],
// //     );
// //   }

// //   Widget _buildSignInButton(BuildContext context, AuthState state) {
// //     return CustomElevatedButton(
// //       isLoading: state.isloginLoading,
// //       isDisabled: state.isloginLoading,
// //       text: Lang.of(context).lbl_login,
// //       buttonTextStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
// //         color: Theme.of(context).customColors.fillColor,
// //         fontSize: 18.0.sp,
// //         fontWeight: FontWeight.w500,
// //       ),
// //       onPressed: () {
// //         FocusManager.instance.primaryFocus?.unfocus();
// //         if (state.addPersonalDetailFormKey.currentState?.validate() ?? false) {
// //           context.read<AuthBloc>().add(LoginSubmitted());
// //         }
// //       },
// //     );
// //   }

// //   Widget _buildSignUpOption(BuildContext context) {
// //     return Row(
// //       mainAxisAlignment: MainAxisAlignment.center,
// //       children: [
// //         Text(
// //           "Don't have an account? ",
// //           style: Theme.of(context).textTheme.bodyMedium?.copyWith(
// //             color: Theme.of(context).customColors.blackColor,
// //             fontSize: 14.sp,
// //           ),
// //         ),
// //         TextButton(
// //           onPressed: () {
// //             ScaffoldMessenger.of(context).showSnackBar(
// //               const SnackBar(content: Text('Navigate to Sign Up screen')),
// //             );
// //           },
// //           style: TextButton.styleFrom(
// //             padding: EdgeInsets.zero,
// //             minimumSize: Size.zero,
// //             tapTargetSize: MaterialTapTargetSize.shrinkWrap,
// //           ),
// //           child: Text(
// //             'Sign Up',
// //             style: Theme.of(context).textTheme.bodyMedium?.copyWith(
// //               color: Theme.of(context).customColors.primaryColor,
// //               fontSize: 14.sp,
// //               fontWeight: FontWeight.w600,
// //             ),
// //           ),
// //         ),
// //       ],
// //     );
// //   }
// // }
