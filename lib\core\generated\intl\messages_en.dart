// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "err_connection_issue": MessageLookupByLibrary.simpleMessage(
      "Connection Issue",
    ),
    "err_connection_issue_description": MessageLookupByLibrary.simpleMessage(
      "We\'re having trouble connecting to the server. Please check your internet connection and try again.",
    ),
    "err_connection_timeout": MessageLookupByLibrary.simpleMessage(
      "Connection Timeout",
    ),
    "err_connection_timeout_description": MessageLookupByLibrary.simpleMessage(
      "Oops! It seems the connection timed out. Please check your internet connection and try again.",
    ),
    "err_data_reception_issue": MessageLookupByLibrary.simpleMessage(
      "Data Reception Issue",
    ),
    "err_data_reception_issue_description": MessageLookupByLibrary.simpleMessage(
      "Oops! We\'re having trouble receiving data right now. Please try again later.",
    ),
    "err_otp_error": MessageLookupByLibrary.simpleMessage(
      "OTP must be exactly 6 digits.",
    ),
    "err_request_cancelled": MessageLookupByLibrary.simpleMessage(
      "Request Cancelled",
    ),
    "err_request_cancelled_description": MessageLookupByLibrary.simpleMessage(
      "Your request has been cancelled. Please try again.",
    ),
    "err_request_timeout": MessageLookupByLibrary.simpleMessage(
      "Request Timeout",
    ),
    "err_request_timeout_description": MessageLookupByLibrary.simpleMessage(
      "Uh-oh! Your request took longer than expected. Please try again later.",
    ),
    "err_security_certificate_problem": MessageLookupByLibrary.simpleMessage(
      "Security Certificate Problem",
    ),
    "err_security_certificate_problem_description":
        MessageLookupByLibrary.simpleMessage(
          "Sorry, there\'s a problem with the security certificate. Please contact support for assistance.",
        ),
    "err_unexpected_server_response": MessageLookupByLibrary.simpleMessage(
      "Unexpected Server Response",
    ),
    "err_unexpected_server_response_description":
        MessageLookupByLibrary.simpleMessage(
          "Oh no! We received an unexpected response from the server. Please try again later.",
        ),
    "err_unknown_error": MessageLookupByLibrary.simpleMessage("Unknown Error"),
    "err_unknown_error_description": MessageLookupByLibrary.simpleMessage(
      "Oops! Something went wrong. Please try again later.",
    ),
    "lbl_Cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
    "lbl_What_is_your_smoking_preference": MessageLookupByLibrary.simpleMessage(
      "What is your smoking preference?",
    ),
    "lbl_add_your_image": MessageLookupByLibrary.simpleMessage(
      "Add your image",
    ),
    "lbl_agree_and_continue": MessageLookupByLibrary.simpleMessage(
      "Agree & Continue",
    ),
    "lbl_all": MessageLookupByLibrary.simpleMessage("All"),
    "lbl_almost_there_email": MessageLookupByLibrary.simpleMessage(
      "Almost there! What\'s your email?",
    ),
    "lbl_app_name": MessageLookupByLibrary.simpleMessage("Room8"),
    "lbl_blocked_accounts": MessageLookupByLibrary.simpleMessage(
      "Blocked Accounts",
    ),
    "lbl_cleanliness_living_style": MessageLookupByLibrary.simpleMessage(
      "Cleanliness & Living Style",
    ),
    "lbl_conf_password": MessageLookupByLibrary.simpleMessage(
      "Confirm Password",
    ),
    "lbl_contact_number": MessageLookupByLibrary.simpleMessage(
      "Contact Number:",
    ),
    "lbl_continue": MessageLookupByLibrary.simpleMessage("Continue"),
    "lbl_country": MessageLookupByLibrary.simpleMessage("Country"),
    "lbl_create_password": MessageLookupByLibrary.simpleMessage(
      "Create a Password",
    ),
    "lbl_crypto": MessageLookupByLibrary.simpleMessage("Crypto"),
    "lbl_declare_confirm": MessageLookupByLibrary.simpleMessage(
      "I declare and confirm.",
    ),
    "lbl_description": MessageLookupByLibrary.simpleMessage("Description"),
    "lbl_did_not_get_otp": MessageLookupByLibrary.simpleMessage(
      "Didn\'t get the OTP? ",
    ),
    "lbl_do_you_have_any_pets": MessageLookupByLibrary.simpleMessage(
      "Do you have any pets?",
    ),
    "lbl_edit_profile": MessageLookupByLibrary.simpleMessage("Edit Profile"),
    "lbl_email": MessageLookupByLibrary.simpleMessage("Email"),
    "lbl_emptyConfPassword": MessageLookupByLibrary.simpleMessage(
      "Please enter confirm password",
    ),
    "lbl_emptyConfirmPassword": MessageLookupByLibrary.simpleMessage(
      "Please enter confirm password",
    ),
    "lbl_emptyEmail": MessageLookupByLibrary.simpleMessage(
      "Please enter an email address",
    ),
    "lbl_emptyName": MessageLookupByLibrary.simpleMessage(
      "Name should not be empty",
    ),
    "lbl_emptyPassword": MessageLookupByLibrary.simpleMessage(
      "Please enter password",
    ),
    "lbl_emptyPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Please enter phone number",
    ),
    "lbl_emptyVerificationCode": MessageLookupByLibrary.simpleMessage(
      "Please enter verification code",
    ),
    "lbl_enter_mobile_number": MessageLookupByLibrary.simpleMessage(
      "Enter mobile number",
    ),
    "lbl_enter_your_email": MessageLookupByLibrary.simpleMessage(
      "Enter Your Email",
    ),
    "lbl_enter_your_first_name": MessageLookupByLibrary.simpleMessage(
      "Enter your first name",
    ),
    "lbl_enter_your_full_name": MessageLookupByLibrary.simpleMessage(
      "Enter your full name",
    ),
    "lbl_enter_your_last_name": MessageLookupByLibrary.simpleMessage(
      "Enter your last name",
    ),
    "lbl_favorite": MessageLookupByLibrary.simpleMessage("Favorite"),
    "lbl_find_the_right_room8": MessageLookupByLibrary.simpleMessage(
      "Find the Right Room8",
    ),
    "lbl_finish_registration": MessageLookupByLibrary.simpleMessage(
      "Finish Registration",
    ),
    "lbl_first_name": MessageLookupByLibrary.simpleMessage("First name"),
    "lbl_forgot_password": MessageLookupByLibrary.simpleMessage(
      "Forgot Password?",
    ),
    "lbl_forgot_password_clicked": MessageLookupByLibrary.simpleMessage(
      "Forgot Password Clicked",
    ),
    "lbl_full_name": MessageLookupByLibrary.simpleMessage("Full name"),
    "lbl_get_help": MessageLookupByLibrary.simpleMessage("Get Help"),
    "lbl_google": MessageLookupByLibrary.simpleMessage("Google"),
    "lbl_habits_lifestyle": MessageLookupByLibrary.simpleMessage(
      "Habits & Lifestyle",
    ),
    "lbl_hello": MessageLookupByLibrary.simpleMessage("Hello!"),
    "lbl_home": MessageLookupByLibrary.simpleMessage("Home"),
    "lbl_information": MessageLookupByLibrary.simpleMessage("Information"),
    "lbl_ingredients": MessageLookupByLibrary.simpleMessage("Ingredients"),
    "lbl_insights": MessageLookupByLibrary.simpleMessage("Insights"),
    "lbl_interests_hobbies": MessageLookupByLibrary.simpleMessage(
      "Interests & Hobbies",
    ),
    "lbl_invalidEmail": MessageLookupByLibrary.simpleMessage(
      "Please enter an valid email address",
    ),
    "lbl_invalidPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Invalid Phone Number",
    ),
    "lbl_last_name": MessageLookupByLibrary.simpleMessage("Last name"),
    "lbl_lease_period": MessageLookupByLibrary.simpleMessage("Lease Period"),
    "lbl_log_out": MessageLookupByLibrary.simpleMessage("Log Out"),
    "lbl_login": MessageLookupByLibrary.simpleMessage("Log in"),
    "lbl_login_or_signup": MessageLookupByLibrary.simpleMessage(
      "Log in or sign up",
    ),
    "lbl_login_redirect": MessageLookupByLibrary.simpleMessage(
      "Already have an account? ",
    ),
    "lbl_logout": MessageLookupByLibrary.simpleMessage("Logout"),
    "lbl_logout_option": MessageLookupByLibrary.simpleMessage("LOGOUT OPTION"),
    "lbl_most_trade": MessageLookupByLibrary.simpleMessage("Most trade"),
    "lbl_move_in": MessageLookupByLibrary.simpleMessage("Move-In"),
    "lbl_move_out": MessageLookupByLibrary.simpleMessage("Move-Out"),
    "lbl_notifications": MessageLookupByLibrary.simpleMessage("Notifications"),
    "lbl_onboarding_title": MessageLookupByLibrary.simpleMessage("Room8"),
    "lbl_or": MessageLookupByLibrary.simpleMessage("Or"),
    "lbl_otp_sent_msg": MessageLookupByLibrary.simpleMessage(
      "We have sent a verification code to",
    ),
    "lbl_otp_verification": MessageLookupByLibrary.simpleMessage(
      "OTP Verification",
    ),
    "lbl_password": MessageLookupByLibrary.simpleMessage("Password"),
    "lbl_passwordMismatch": MessageLookupByLibrary.simpleMessage(
      "Passwords do not match",
    ),
    "lbl_personal_details": MessageLookupByLibrary.simpleMessage(
      "Personal details",
    ),
    "lbl_photo_upload": MessageLookupByLibrary.simpleMessage("Photo Upload"),
    "lbl_pick_5_things": MessageLookupByLibrary.simpleMessage("Pick 5 Things:"),
    "lbl_pin_4_digits": MessageLookupByLibrary.simpleMessage(
      "PIN must be 4 digits",
    ),
    "lbl_pin_only_digits": MessageLookupByLibrary.simpleMessage(
      "PIN should contain only digits",
    ),
    "lbl_preferred_lease_period": MessageLookupByLibrary.simpleMessage(
      "Preferred Lease Period",
    ),
    "lbl_privacy_policy": MessageLookupByLibrary.simpleMessage(
      "Privacy Policy",
    ),
    "lbl_protect_with_pin": MessageLookupByLibrary.simpleMessage(
      "Protect your app with a PIN.",
    ),
    "lbl_register": MessageLookupByLibrary.simpleMessage("Register"),
    "lbl_resend_sms": MessageLookupByLibrary.simpleMessage("Resend SMS"),
    "lbl_save": MessageLookupByLibrary.simpleMessage("Save"),
    "lbl_search": MessageLookupByLibrary.simpleMessage("Search"),
    "lbl_search_country": MessageLookupByLibrary.simpleMessage(
      "Search Country",
    ),
    "lbl_secure_account_password": MessageLookupByLibrary.simpleMessage(
      "Secure your account with a strong password.",
    ),
    "lbl_select_country": MessageLookupByLibrary.simpleMessage(
      "Select Country",
    ),
    "lbl_select_your_country": MessageLookupByLibrary.simpleMessage(
      "Select Your Country",
    ),
    "lbl_selected": MessageLookupByLibrary.simpleMessage("Selected"),
    "lbl_set_app_lock_pin": MessageLookupByLibrary.simpleMessage(
      "Set App Lock PIN",
    ),
    "lbl_setting_subtitle": MessageLookupByLibrary.simpleMessage(
      "SMS, Reminders, PIP",
    ),
    "lbl_setting_title": MessageLookupByLibrary.simpleMessage("Setting"),
    "lbl_settings": MessageLookupByLibrary.simpleMessage("Settings"),
    "lbl_show_more": MessageLookupByLibrary.simpleMessage("Show more"),
    "lbl_sign_up": MessageLookupByLibrary.simpleMessage("Sign up"),
    "lbl_signup_message": MessageLookupByLibrary.simpleMessage(
      "Create your profile and discover compatible Room8 nearby.",
    ),
    "lbl_signup_redirect": MessageLookupByLibrary.simpleMessage(
      "Don\'t have an account? ",
    ),
    "lbl_skip": MessageLookupByLibrary.simpleMessage("Skip"),
    "lbl_tell_us_about_your_self": MessageLookupByLibrary.simpleMessage(
      "Tell Us About Your Self",
    ),
    "lbl_terms_condition": MessageLookupByLibrary.simpleMessage(
      "Terms & Condition",
    ),
    "lbl_that_matter_to_you_when_living_with_a_room8":
        MessageLookupByLibrary.simpleMessage(
          "That matter to you when living with a room8",
        ),
    "lbl_top_movers": MessageLookupByLibrary.simpleMessage("Top Movers"),
    "lbl_top_news": MessageLookupByLibrary.simpleMessage("Top News"),
    "lbl_trade": MessageLookupByLibrary.simpleMessage("Trade"),
    "lbl_trading_signals": MessageLookupByLibrary.simpleMessage(
      "Trading Signals",
    ),
    "lbl_upcoming_event": MessageLookupByLibrary.simpleMessage(
      "Upcoming Event",
    ),
    "lbl_use_8_15_characters": MessageLookupByLibrary.simpleMessage(
      "Use from 8 to 15 characters",
    ),
    "lbl_use_number_special": MessageLookupByLibrary.simpleMessage(
      "Use a combination of numbers and special characters",
    ),
    "lbl_use_upper_lower": MessageLookupByLibrary.simpleMessage(
      "Use both uppercase and lowercase letters",
    ),
    "lbl_verify_otp": MessageLookupByLibrary.simpleMessage("Verify OTP"),
    "lbl_welcome_get_started": MessageLookupByLibrary.simpleMessage(
      "Welcome! Let\'s get started.",
    ),
    "lbl_welcome_message": MessageLookupByLibrary.simpleMessage(
      "Welcome to Room8",
    ),
    "lbl_welcome_message_desc": MessageLookupByLibrary.simpleMessage(
      "Let’s make finding your next home and Room8 safe and simple. Follow these community values.",
    ),
    "lbl_what_are_your_interests": MessageLookupByLibrary.simpleMessage(
      "What are your interests?",
    ),
    "lbl_what_is_your_cleanliness_level": MessageLookupByLibrary.simpleMessage(
      "What is your cleanliness level?",
    ),
    "lbl_what_is_your_current_class_standing":
        MessageLookupByLibrary.simpleMessage(
          "What is your current class standing?",
        ),
    "lbl_what_is_your_date_of_birth": MessageLookupByLibrary.simpleMessage(
      "What is your Date of Birth?",
    ),
    "lbl_what_is_your_full_name": MessageLookupByLibrary.simpleMessage(
      "What is your Full Name?",
    ),
    "lbl_whats_your_first_name": MessageLookupByLibrary.simpleMessage(
      "What\'s your first name?",
    ),
    "lbl_whats_your_last_name": MessageLookupByLibrary.simpleMessage(
      "What\'s your last name?",
    ),
    "lbl_who_you_want_for_room_mate_seeing":
        MessageLookupByLibrary.simpleMessage("Who you want for room8 seeing?"),
    "lbl_your_personal_details": MessageLookupByLibrary.simpleMessage(
      "Your Personal Details",
    ),
    "lbl_your_personal_details_desc": MessageLookupByLibrary.simpleMessage(
      "Let’s get to know you a bit better - this helps us find your most compatible Room8.",
    ),
    "lbl_your_personal_details_desc2": MessageLookupByLibrary.simpleMessage(
      "Help us match you with Room8 who vibe with your habits and routines.",
    ),
    "lbl_your_personal_details_desc3": MessageLookupByLibrary.simpleMessage(
      "Tell us about your lifestyle - we’ll match you with people who vibe with it.",
    ),
    "msg_logout_subtitle": MessageLookupByLibrary.simpleMessage(
      "You will need to sign in again to access your account.",
    ),
    "msg_logout_title": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to log out?",
    ),
    "msg_onboarding_description": MessageLookupByLibrary.simpleMessage(
      "Browse verified profiles, connect with like-minded people, and enjoy stress-free shared living.",
    ),
    "msg_onboarding_description2": MessageLookupByLibrary.simpleMessage(
      "Connect with like-minded people and share your space with confidence.",
    ),
    "msg_onboarding_title": MessageLookupByLibrary.simpleMessage(
      "Find your perfect Room8 with ease.",
    ),
    "msg_onboarding_title2": MessageLookupByLibrary.simpleMessage(
      "Find your ideal Room8 effortlessly",
    ),
  };
}
