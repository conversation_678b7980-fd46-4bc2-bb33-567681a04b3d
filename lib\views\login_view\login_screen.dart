import 'dart:ui';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/widgets/custom_widget/custom_gradient_container.dart';

class LoginScreen extends StatelessWidget {
  const LoginScreen({super.key});

  static Widget builder(BuildContext context) => const LoginScreen();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        return Scaffold(
          backgroundColor: Theme.of(context).customColors.fillColor,
          body: AbsorbPointer(
            absorbing: state.isloginLoading,
            child: Form(
              key: state.loginFormKey,
              child: SingleChildScrollView(
                child: BackgroundImage(
                  imagePath: Assets.images.pngs.other.pngLoginBg.path,
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      minHeight: MediaQuery.of(context).size.height,
                    ),
                    child: IntrinsicHeight(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          _buildTopSection(context),
                          _buildLoginHeader(context),
                          _buildLoginForm(context, state),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTopSection(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: 16.w,
        right: 16.w,
        top: 60.h,
        bottom: 16.h,
      ),
      child: _buildTopBar(context),
    );
  }

  Widget _buildTopBar(BuildContext context) {
    final customColors = Theme.of(context).customColors;

    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        CustomGradientContainer(
          height: 36.w,
          width: 36.w,
          topColor: customColors.fillColor!.withAlpha((0.6 * 255).toInt()),
          bottomColor: customColors.blackColor!.withAlpha((0.4 * 255).toInt()),
          fillColor: customColors.fillColor!.withAlpha(75),
          child: CustomImageView(
            imagePath: Assets.images.svgs.icons.icForwardArrow.path,
          ),
        ),
      ],
    );
  }

  Widget _buildLoginHeader(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: 16.w,
        right: 16.w,
        top: 16.h,
        bottom: 32.h,
      ),
      child: Column(
        children: [
          Text(
            Lang.of(context).lbl_hello,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              fontSize: 40.sp,
              color: Theme.of(context).customColors.fillColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            Lang.of(context).lbl_welcome_message,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              fontSize: 16.sp,
              color: Theme.of(context).customColors.fillColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoginForm(BuildContext context, AuthState state) {
    return Flexible(
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 7, sigmaY: 7),
          child: Container(
            width: double.infinity,
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height * 0.6,
            ),
            decoration: BoxDecoration(
              color: Theme.of(
                context,
              ).customColors.fillColor?.withValues(alpha: 0.8),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildDragHandle(context),
                  buildSizedBoxH(24.h),
                  _buildLoginEmailField(context, state),
                  buildSizedBoxH(16.h),
                  _buildLoginPasswordField(context, state),
                  buildSizedBoxH(4.h),
                  _buildForgotPassword(context),
                  buildSizedBoxH(24.h),
                  const Spacer(),
                  _buildSignInButton(context, state),
                  buildSizedBoxH(16.h),
                  _buildSignUpOption(context),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDragHandle(BuildContext context) {
    return Text(
      Lang.of(context).lbl_login,
      textAlign: TextAlign.center,
      style: Theme.of(context).textTheme.labelLarge?.copyWith(
        fontSize: 22.sp,
        color: Theme.of(context).customColors.blackColor,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildLoginEmailField(BuildContext context, AuthState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'School ID',
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontSize: 16.sp,
            color: Theme.of(context).customColors.blackColor,
          ),
        ),
        CustomTextInputField(
          context: context,
          type: InputType.email,
          hintLabel: Lang.of(context).lbl_emptyEmail,
          controller: state.emailController,
          focusNode: state.emailFocusNode,
          textInputAction: TextInputAction.next,
          prefixIcon: CustomImageView(
            margin: EdgeInsets.all(12.0),
            imagePath: Assets.images.svgs.icons.icMail.path,
          ),
          validator: (value) => AppValidations.emailValidation(value, context),
          onChanged: (value) =>
              context.read<AuthBloc>().add(EmailChanged(value)),
        ),
      ],
    );
  }

  Widget _buildLoginPasswordField(BuildContext context, AuthState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_password,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontSize: 16.sp,
            color: Theme.of(context).customColors.blackColor,
          ),
        ),
        CustomTextInputField(
          context: context,
          type: InputType.password,
          hintLabel: Lang.of(context).lbl_emptyPassword,
          controller: state.passwordController,
          focusNode: state.passwordFocusNode,
          textInputAction: TextInputAction.done,
          obscureText: state.obscurePassword,
          prefixIcon: CustomImageView(
            margin: EdgeInsets.all(12.0),
            imagePath: Assets.images.svgs.icons.icLock.path,
          ),
          validator: (value) =>
              AppValidations.passwordValidation(value, context),
          onChanged: (value) =>
              context.read<AuthBloc>().add(PasswordChanged(value)),
          suffixIcon: CustomImageView(
            margin: EdgeInsets.all(12.0),
            imagePath: state.obscurePassword
                ? Assets.images.svgs.icons.icEyeOff.path
                : Assets.images.svgs.icons.icEyeOn.path,
            onTap: () {
              context.read<AuthBloc>().add(TogglePasswordVisibility());
            },
          ),
        ),
      ],
    );
  }

  Widget _buildForgotPassword(BuildContext context) {
    return Align(
      alignment: Alignment.centerRight,
      child: TextButton(
        onPressed: () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(Lang.of(context).lbl_forgot_password_clicked),
            ),
          );
        },
        child: Text(
          Lang.of(context).lbl_forgot_password,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).customColors.primaryColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildSignInButton(BuildContext context, AuthState state) {
    return CustomElevatedButton(
      isLoading: state.isloginLoading,
      isDisabled: state.isloginLoading,
      text: Lang.of(context).lbl_login,
      buttonTextStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
        color: Theme.of(context).customColors.fillColor,
        fontSize: 18.0.sp,
        fontWeight: FontWeight.w500,
      ),
      onPressed: () {
        FocusManager.instance.primaryFocus?.unfocus();
        if (state.loginFormKey.currentState?.validate() ?? false) {
          context.read<AuthBloc>().add(LoginSubmitted());
        }
      },
    );
  }

  Widget _buildSignUpOption(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          Lang.of(context).lbl_signup_redirect,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).customColors.blackColor,
            fontSize: 14.sp,
          ),
        ),
        TextButton(
          onPressed: () {
            NavigatorService.pushNamed(AppRoutes.signupScreen);
          },
          style: TextButton.styleFrom(
            padding: EdgeInsets.zero,
            minimumSize: Size.zero,
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          child: Text(
            Lang.of(context).lbl_sign_up,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).customColors.primaryColor,
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}
