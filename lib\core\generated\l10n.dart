// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class Lang {
  Lang();

  static Lang? _current;

  static Lang get current {
    assert(
      _current != null,
      'No instance of <PERSON> was loaded. Try to initialize the Lang delegate before accessing Lang.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<Lang> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = Lang();
      Lang._current = instance;

      return instance;
    });
  }

  static Lang of(BuildContext context) {
    final instance = Lang.maybeOf(context);
    assert(
      instance != null,
      'No instance of Lang present in the widget tree. Did you add Lang.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static Lang? maybeOf(BuildContext context) {
    return Localizations.of<Lang>(context, Lang);
  }

  /// `Room8`
  String get lbl_app_name {
    return Intl.message('Room8', name: 'lbl_app_name', desc: '', args: []);
  }

  /// `Oops! It seems the connection timed out. Please check your internet connection and try again.`
  String get err_connection_timeout_description {
    return Intl.message(
      'Oops! It seems the connection timed out. Please check your internet connection and try again.',
      name: 'err_connection_timeout_description',
      desc: '',
      args: [],
    );
  }

  /// `Uh-oh! Your request took longer than expected. Please try again later.`
  String get err_request_timeout_description {
    return Intl.message(
      'Uh-oh! Your request took longer than expected. Please try again later.',
      name: 'err_request_timeout_description',
      desc: '',
      args: [],
    );
  }

  /// `Oops! We're having trouble receiving data right now. Please try again later.`
  String get err_data_reception_issue_description {
    return Intl.message(
      'Oops! We\'re having trouble receiving data right now. Please try again later.',
      name: 'err_data_reception_issue_description',
      desc: '',
      args: [],
    );
  }

  /// `Sorry, there's a problem with the security certificate. Please contact support for assistance.`
  String get err_security_certificate_problem_description {
    return Intl.message(
      'Sorry, there\'s a problem with the security certificate. Please contact support for assistance.',
      name: 'err_security_certificate_problem_description',
      desc: '',
      args: [],
    );
  }

  /// `Oh no! We received an unexpected response from the server. Please try again later.`
  String get err_unexpected_server_response_description {
    return Intl.message(
      'Oh no! We received an unexpected response from the server. Please try again later.',
      name: 'err_unexpected_server_response_description',
      desc: '',
      args: [],
    );
  }

  /// `Your request has been cancelled. Please try again.`
  String get err_request_cancelled_description {
    return Intl.message(
      'Your request has been cancelled. Please try again.',
      name: 'err_request_cancelled_description',
      desc: '',
      args: [],
    );
  }

  /// `We're having trouble connecting to the server. Please check your internet connection and try again.`
  String get err_connection_issue_description {
    return Intl.message(
      'We\'re having trouble connecting to the server. Please check your internet connection and try again.',
      name: 'err_connection_issue_description',
      desc: '',
      args: [],
    );
  }

  /// `Oops! Something went wrong. Please try again later.`
  String get err_unknown_error_description {
    return Intl.message(
      'Oops! Something went wrong. Please try again later.',
      name: 'err_unknown_error_description',
      desc: '',
      args: [],
    );
  }

  /// `Connection Timeout`
  String get err_connection_timeout {
    return Intl.message(
      'Connection Timeout',
      name: 'err_connection_timeout',
      desc: '',
      args: [],
    );
  }

  /// `Request Timeout`
  String get err_request_timeout {
    return Intl.message(
      'Request Timeout',
      name: 'err_request_timeout',
      desc: '',
      args: [],
    );
  }

  /// `Data Reception Issue`
  String get err_data_reception_issue {
    return Intl.message(
      'Data Reception Issue',
      name: 'err_data_reception_issue',
      desc: '',
      args: [],
    );
  }

  /// `Security Certificate Problem`
  String get err_security_certificate_problem {
    return Intl.message(
      'Security Certificate Problem',
      name: 'err_security_certificate_problem',
      desc: '',
      args: [],
    );
  }

  /// `Unexpected Server Response`
  String get err_unexpected_server_response {
    return Intl.message(
      'Unexpected Server Response',
      name: 'err_unexpected_server_response',
      desc: '',
      args: [],
    );
  }

  /// `Request Cancelled`
  String get err_request_cancelled {
    return Intl.message(
      'Request Cancelled',
      name: 'err_request_cancelled',
      desc: '',
      args: [],
    );
  }

  /// `Connection Issue`
  String get err_connection_issue {
    return Intl.message(
      'Connection Issue',
      name: 'err_connection_issue',
      desc: '',
      args: [],
    );
  }

  /// `Unknown Error`
  String get err_unknown_error {
    return Intl.message(
      'Unknown Error',
      name: 'err_unknown_error',
      desc: '',
      args: [],
    );
  }

  /// `OTP must be exactly 6 digits.`
  String get err_otp_error {
    return Intl.message(
      'OTP must be exactly 6 digits.',
      name: 'err_otp_error',
      desc: '',
      args: [],
    );
  }

  /// `Register`
  String get lbl_register {
    return Intl.message('Register', name: 'lbl_register', desc: '', args: []);
  }

  /// `Sign up`
  String get lbl_sign_up {
    return Intl.message('Sign up', name: 'lbl_sign_up', desc: '', args: []);
  }

  /// `Google`
  String get lbl_google {
    return Intl.message('Google', name: 'lbl_google', desc: '', args: []);
  }

  /// `Or`
  String get lbl_or {
    return Intl.message('Or', name: 'lbl_or', desc: '', args: []);
  }

  /// `Room8`
  String get lbl_onboarding_title {
    return Intl.message(
      'Room8',
      name: 'lbl_onboarding_title',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get lbl_continue {
    return Intl.message('Continue', name: 'lbl_continue', desc: '', args: []);
  }

  /// `OTP Verification`
  String get lbl_otp_verification {
    return Intl.message(
      'OTP Verification',
      name: 'lbl_otp_verification',
      desc: '',
      args: [],
    );
  }

  /// `Log in`
  String get lbl_login {
    return Intl.message('Log in', name: 'lbl_login', desc: '', args: []);
  }

  /// `Log in or sign up`
  String get lbl_login_or_signup {
    return Intl.message(
      'Log in or sign up',
      name: 'lbl_login_or_signup',
      desc: '',
      args: [],
    );
  }

  /// `We have sent a verification code to`
  String get lbl_otp_sent_msg {
    return Intl.message(
      'We have sent a verification code to',
      name: 'lbl_otp_sent_msg',
      desc: '',
      args: [],
    );
  }

  /// `Didn't get the OTP? `
  String get lbl_did_not_get_otp {
    return Intl.message(
      'Didn\'t get the OTP? ',
      name: 'lbl_did_not_get_otp',
      desc: '',
      args: [],
    );
  }

  /// `Resend SMS`
  String get lbl_resend_sms {
    return Intl.message(
      'Resend SMS',
      name: 'lbl_resend_sms',
      desc: '',
      args: [],
    );
  }

  /// `Verify OTP`
  String get lbl_verify_otp {
    return Intl.message(
      'Verify OTP',
      name: 'lbl_verify_otp',
      desc: '',
      args: [],
    );
  }

  /// `Contact Number:`
  String get lbl_contact_number {
    return Intl.message(
      'Contact Number:',
      name: 'lbl_contact_number',
      desc: '',
      args: [],
    );
  }

  /// `Personal details`
  String get lbl_personal_details {
    return Intl.message(
      'Personal details',
      name: 'lbl_personal_details',
      desc: '',
      args: [],
    );
  }

  /// `What's your first name?`
  String get lbl_whats_your_first_name {
    return Intl.message(
      'What\'s your first name?',
      name: 'lbl_whats_your_first_name',
      desc: '',
      args: [],
    );
  }

  /// `Enter your first name`
  String get lbl_enter_your_first_name {
    return Intl.message(
      'Enter your first name',
      name: 'lbl_enter_your_first_name',
      desc: '',
      args: [],
    );
  }

  /// `What's your last name?`
  String get lbl_whats_your_last_name {
    return Intl.message(
      'What\'s your last name?',
      name: 'lbl_whats_your_last_name',
      desc: '',
      args: [],
    );
  }

  /// `Enter your last name`
  String get lbl_enter_your_last_name {
    return Intl.message(
      'Enter your last name',
      name: 'lbl_enter_your_last_name',
      desc: '',
      args: [],
    );
  }

  /// `Please enter verification code`
  String get lbl_emptyVerificationCode {
    return Intl.message(
      'Please enter verification code',
      name: 'lbl_emptyVerificationCode',
      desc: '',
      args: [],
    );
  }

  /// `Please enter phone number`
  String get lbl_emptyPhoneNumber {
    return Intl.message(
      'Please enter phone number',
      name: 'lbl_emptyPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Invalid Phone Number`
  String get lbl_invalidPhoneNumber {
    return Intl.message(
      'Invalid Phone Number',
      name: 'lbl_invalidPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Name should not be empty`
  String get lbl_emptyName {
    return Intl.message(
      'Name should not be empty',
      name: 'lbl_emptyName',
      desc: '',
      args: [],
    );
  }

  /// `Please enter password`
  String get lbl_emptyPassword {
    return Intl.message(
      'Please enter password',
      name: 'lbl_emptyPassword',
      desc: '',
      args: [],
    );
  }

  /// `Please enter confirm password`
  String get lbl_emptyConfPassword {
    return Intl.message(
      'Please enter confirm password',
      name: 'lbl_emptyConfPassword',
      desc: '',
      args: [],
    );
  }

  /// `Please enter confirm password`
  String get lbl_emptyConfirmPassword {
    return Intl.message(
      'Please enter confirm password',
      name: 'lbl_emptyConfirmPassword',
      desc: '',
      args: [],
    );
  }

  /// `Passwords do not match`
  String get lbl_passwordMismatch {
    return Intl.message(
      'Passwords do not match',
      name: 'lbl_passwordMismatch',
      desc: '',
      args: [],
    );
  }

  /// `Please enter an email address`
  String get lbl_emptyEmail {
    return Intl.message(
      'Please enter an email address',
      name: 'lbl_emptyEmail',
      desc: '',
      args: [],
    );
  }

  /// `Please enter an valid email address`
  String get lbl_invalidEmail {
    return Intl.message(
      'Please enter an valid email address',
      name: 'lbl_invalidEmail',
      desc: '',
      args: [],
    );
  }

  /// `Enter mobile number`
  String get lbl_enter_mobile_number {
    return Intl.message(
      'Enter mobile number',
      name: 'lbl_enter_mobile_number',
      desc: '',
      args: [],
    );
  }

  /// `Home`
  String get lbl_home {
    return Intl.message('Home', name: 'lbl_home', desc: '', args: []);
  }

  /// `Search Country`
  String get lbl_search_country {
    return Intl.message(
      'Search Country',
      name: 'lbl_search_country',
      desc: '',
      args: [],
    );
  }

  /// `First name`
  String get lbl_first_name {
    return Intl.message(
      'First name',
      name: 'lbl_first_name',
      desc: '',
      args: [],
    );
  }

  /// `Last name`
  String get lbl_last_name {
    return Intl.message('Last name', name: 'lbl_last_name', desc: '', args: []);
  }

  /// `Cancel`
  String get lbl_Cancel {
    return Intl.message('Cancel', name: 'lbl_Cancel', desc: '', args: []);
  }

  /// `Logout`
  String get lbl_logout {
    return Intl.message('Logout', name: 'lbl_logout', desc: '', args: []);
  }

  /// `Edit Profile`
  String get lbl_edit_profile {
    return Intl.message(
      'Edit Profile',
      name: 'lbl_edit_profile',
      desc: '',
      args: [],
    );
  }

  /// `Setting`
  String get lbl_setting_title {
    return Intl.message(
      'Setting',
      name: 'lbl_setting_title',
      desc: '',
      args: [],
    );
  }

  /// `SMS, Reminders, PIP`
  String get lbl_setting_subtitle {
    return Intl.message(
      'SMS, Reminders, PIP',
      name: 'lbl_setting_subtitle',
      desc: '',
      args: [],
    );
  }

  /// `LOGOUT OPTION`
  String get lbl_logout_option {
    return Intl.message(
      'LOGOUT OPTION',
      name: 'lbl_logout_option',
      desc: '',
      args: [],
    );
  }

  /// `Description`
  String get lbl_description {
    return Intl.message(
      'Description',
      name: 'lbl_description',
      desc: '',
      args: [],
    );
  }

  /// `Ingredients`
  String get lbl_ingredients {
    return Intl.message(
      'Ingredients',
      name: 'lbl_ingredients',
      desc: '',
      args: [],
    );
  }

  /// `Information`
  String get lbl_information {
    return Intl.message(
      'Information',
      name: 'lbl_information',
      desc: '',
      args: [],
    );
  }

  /// `Search`
  String get lbl_search {
    return Intl.message('Search', name: 'lbl_search', desc: '', args: []);
  }

  /// `Select Your Country`
  String get lbl_select_your_country {
    return Intl.message(
      'Select Your Country',
      name: 'lbl_select_your_country',
      desc: '',
      args: [],
    );
  }

  /// `Welcome! Let's get started.`
  String get lbl_welcome_get_started {
    return Intl.message(
      'Welcome! Let\'s get started.',
      name: 'lbl_welcome_get_started',
      desc: '',
      args: [],
    );
  }

  /// `Country`
  String get lbl_country {
    return Intl.message('Country', name: 'lbl_country', desc: '', args: []);
  }

  /// `Select Country`
  String get lbl_select_country {
    return Intl.message(
      'Select Country',
      name: 'lbl_select_country',
      desc: '',
      args: [],
    );
  }

  /// `I declare and confirm.`
  String get lbl_declare_confirm {
    return Intl.message(
      'I declare and confirm.',
      name: 'lbl_declare_confirm',
      desc: '',
      args: [],
    );
  }

  /// `Enter Your Email`
  String get lbl_enter_your_email {
    return Intl.message(
      'Enter Your Email',
      name: 'lbl_enter_your_email',
      desc: '',
      args: [],
    );
  }

  /// `Almost there! What's your email?`
  String get lbl_almost_there_email {
    return Intl.message(
      'Almost there! What\'s your email?',
      name: 'lbl_almost_there_email',
      desc: '',
      args: [],
    );
  }

  /// `Email`
  String get lbl_email {
    return Intl.message('Email', name: 'lbl_email', desc: '', args: []);
  }

  /// `Create a Password`
  String get lbl_create_password {
    return Intl.message(
      'Create a Password',
      name: 'lbl_create_password',
      desc: '',
      args: [],
    );
  }

  /// `Secure your account with a strong password.`
  String get lbl_secure_account_password {
    return Intl.message(
      'Secure your account with a strong password.',
      name: 'lbl_secure_account_password',
      desc: '',
      args: [],
    );
  }

  /// `Password`
  String get lbl_password {
    return Intl.message('Password', name: 'lbl_password', desc: '', args: []);
  }

  /// `Confirm Password`
  String get lbl_conf_password {
    return Intl.message(
      'Confirm Password',
      name: 'lbl_conf_password',
      desc: '',
      args: [],
    );
  }

  /// `Use from 8 to 15 characters`
  String get lbl_use_8_15_characters {
    return Intl.message(
      'Use from 8 to 15 characters',
      name: 'lbl_use_8_15_characters',
      desc: '',
      args: [],
    );
  }

  /// `Use both uppercase and lowercase letters`
  String get lbl_use_upper_lower {
    return Intl.message(
      'Use both uppercase and lowercase letters',
      name: 'lbl_use_upper_lower',
      desc: '',
      args: [],
    );
  }

  /// `Use a combination of numbers and special characters`
  String get lbl_use_number_special {
    return Intl.message(
      'Use a combination of numbers and special characters',
      name: 'lbl_use_number_special',
      desc: '',
      args: [],
    );
  }

  /// `Set App Lock PIN`
  String get lbl_set_app_lock_pin {
    return Intl.message(
      'Set App Lock PIN',
      name: 'lbl_set_app_lock_pin',
      desc: '',
      args: [],
    );
  }

  /// `Protect your app with a PIN.`
  String get lbl_protect_with_pin {
    return Intl.message(
      'Protect your app with a PIN.',
      name: 'lbl_protect_with_pin',
      desc: '',
      args: [],
    );
  }

  /// `PIN must be 4 digits`
  String get lbl_pin_4_digits {
    return Intl.message(
      'PIN must be 4 digits',
      name: 'lbl_pin_4_digits',
      desc: '',
      args: [],
    );
  }

  /// `PIN should contain only digits`
  String get lbl_pin_only_digits {
    return Intl.message(
      'PIN should contain only digits',
      name: 'lbl_pin_only_digits',
      desc: '',
      args: [],
    );
  }

  /// `Finish Registration`
  String get lbl_finish_registration {
    return Intl.message(
      'Finish Registration',
      name: 'lbl_finish_registration',
      desc: '',
      args: [],
    );
  }

  /// `Forgot Password?`
  String get lbl_forgot_password {
    return Intl.message(
      'Forgot Password?',
      name: 'lbl_forgot_password',
      desc: '',
      args: [],
    );
  }

  /// `Forgot Password Clicked`
  String get lbl_forgot_password_clicked {
    return Intl.message(
      'Forgot Password Clicked',
      name: 'lbl_forgot_password_clicked',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to log out?`
  String get msg_logout_title {
    return Intl.message(
      'Are you sure you want to log out?',
      name: 'msg_logout_title',
      desc: '',
      args: [],
    );
  }

  /// `You will need to sign in again to access your account.`
  String get msg_logout_subtitle {
    return Intl.message(
      'You will need to sign in again to access your account.',
      name: 'msg_logout_subtitle',
      desc: '',
      args: [],
    );
  }

  /// `Trade`
  String get lbl_trade {
    return Intl.message('Trade', name: 'lbl_trade', desc: '', args: []);
  }

  /// `Favorite`
  String get lbl_favorite {
    return Intl.message('Favorite', name: 'lbl_favorite', desc: '', args: []);
  }

  /// `Most trade`
  String get lbl_most_trade {
    return Intl.message(
      'Most trade',
      name: 'lbl_most_trade',
      desc: '',
      args: [],
    );
  }

  /// `Top Movers`
  String get lbl_top_movers {
    return Intl.message(
      'Top Movers',
      name: 'lbl_top_movers',
      desc: '',
      args: [],
    );
  }

  /// `Crypto`
  String get lbl_crypto {
    return Intl.message('Crypto', name: 'lbl_crypto', desc: '', args: []);
  }

  /// `All`
  String get lbl_all {
    return Intl.message('All', name: 'lbl_all', desc: '', args: []);
  }

  /// `Insights`
  String get lbl_insights {
    return Intl.message('Insights', name: 'lbl_insights', desc: '', args: []);
  }

  /// `Show more`
  String get lbl_show_more {
    return Intl.message('Show more', name: 'lbl_show_more', desc: '', args: []);
  }

  /// `Trading Signals`
  String get lbl_trading_signals {
    return Intl.message(
      'Trading Signals',
      name: 'lbl_trading_signals',
      desc: '',
      args: [],
    );
  }

  /// `Upcoming Event`
  String get lbl_upcoming_event {
    return Intl.message(
      'Upcoming Event',
      name: 'lbl_upcoming_event',
      desc: '',
      args: [],
    );
  }

  /// `Top News`
  String get lbl_top_news {
    return Intl.message('Top News', name: 'lbl_top_news', desc: '', args: []);
  }

  /// `Skip`
  String get lbl_skip {
    return Intl.message('Skip', name: 'lbl_skip', desc: '', args: []);
  }

  /// `Find your perfect Room8 with ease.`
  String get msg_onboarding_title {
    return Intl.message(
      'Find your perfect Room8 with ease.',
      name: 'msg_onboarding_title',
      desc: '',
      args: [],
    );
  }

  /// `Browse verified profiles, connect with like-minded people, and enjoy stress-free shared living.`
  String get msg_onboarding_description {
    return Intl.message(
      'Browse verified profiles, connect with like-minded people, and enjoy stress-free shared living.',
      name: 'msg_onboarding_description',
      desc: '',
      args: [],
    );
  }

  /// `Find your ideal Room8 effortlessly`
  String get msg_onboarding_title2 {
    return Intl.message(
      'Find your ideal Room8 effortlessly',
      name: 'msg_onboarding_title2',
      desc: '',
      args: [],
    );
  }

  /// `Connect with like-minded people and share your space with confidence.`
  String get msg_onboarding_description2 {
    return Intl.message(
      'Connect with like-minded people and share your space with confidence.',
      name: 'msg_onboarding_description2',
      desc: '',
      args: [],
    );
  }

  /// `Hello!`
  String get lbl_hello {
    return Intl.message('Hello!', name: 'lbl_hello', desc: '', args: []);
  }

  /// `Welcome to Room8`
  String get lbl_welcome_message {
    return Intl.message(
      'Welcome to Room8',
      name: 'lbl_welcome_message',
      desc: '',
      args: [],
    );
  }

  /// `Create your profile and discover compatible Room8 nearby.`
  String get lbl_signup_message {
    return Intl.message(
      'Create your profile and discover compatible Room8 nearby.',
      name: 'lbl_signup_message',
      desc: '',
      args: [],
    );
  }

  /// `Already have an account? `
  String get lbl_login_redirect {
    return Intl.message(
      'Already have an account? ',
      name: 'lbl_login_redirect',
      desc: '',
      args: [],
    );
  }

  /// `Don't have an account? `
  String get lbl_signup_redirect {
    return Intl.message(
      'Don\'t have an account? ',
      name: 'lbl_signup_redirect',
      desc: '',
      args: [],
    );
  }

  /// `Let’s make finding your next home and Room8 safe and simple. Follow these community values.`
  String get lbl_welcome_message_desc {
    return Intl.message(
      'Let’s make finding your next home and Room8 safe and simple. Follow these community values.',
      name: 'lbl_welcome_message_desc',
      desc: '',
      args: [],
    );
  }

  /// `Agree & Continue`
  String get lbl_agree_and_continue {
    return Intl.message(
      'Agree & Continue',
      name: 'lbl_agree_and_continue',
      desc: '',
      args: [],
    );
  }

  /// `Your Personal Details`
  String get lbl_your_personal_details {
    return Intl.message(
      'Your Personal Details',
      name: 'lbl_your_personal_details',
      desc: '',
      args: [],
    );
  }

  /// `Let’s get to know you a bit better - this helps us find your most compatible Room8.`
  String get lbl_your_personal_details_desc {
    return Intl.message(
      'Let’s get to know you a bit better - this helps us find your most compatible Room8.',
      name: 'lbl_your_personal_details_desc',
      desc: '',
      args: [],
    );
  }

  /// `Add your image`
  String get lbl_add_your_image {
    return Intl.message(
      'Add your image',
      name: 'lbl_add_your_image',
      desc: '',
      args: [],
    );
  }

  /// `What is your Full Name?`
  String get lbl_what_is_your_full_name {
    return Intl.message(
      'What is your Full Name?',
      name: 'lbl_what_is_your_full_name',
      desc: '',
      args: [],
    );
  }

  /// `Full name`
  String get lbl_full_name {
    return Intl.message('Full name', name: 'lbl_full_name', desc: '', args: []);
  }

  /// `Enter your full name`
  String get lbl_enter_your_full_name {
    return Intl.message(
      'Enter your full name',
      name: 'lbl_enter_your_full_name',
      desc: '',
      args: [],
    );
  }

  /// `What is your Date of Birth?`
  String get lbl_what_is_your_date_of_birth {
    return Intl.message(
      'What is your Date of Birth?',
      name: 'lbl_what_is_your_date_of_birth',
      desc: '',
      args: [],
    );
  }

  /// `Tell Us About Your Self`
  String get lbl_tell_us_about_your_self {
    return Intl.message(
      'Tell Us About Your Self',
      name: 'lbl_tell_us_about_your_self',
      desc: '',
      args: [],
    );
  }

  /// `Help us match you with Room8 who vibe with your habits and routines.`
  String get lbl_your_personal_details_desc2 {
    return Intl.message(
      'Help us match you with Room8 who vibe with your habits and routines.',
      name: 'lbl_your_personal_details_desc2',
      desc: '',
      args: [],
    );
  }

  /// `Preferred Lease Period`
  String get lbl_preferred_lease_period {
    return Intl.message(
      'Preferred Lease Period',
      name: 'lbl_preferred_lease_period',
      desc: '',
      args: [],
    );
  }

  /// `Who you want for room8 seeing?`
  String get lbl_who_you_want_for_room_mate_seeing {
    return Intl.message(
      'Who you want for room8 seeing?',
      name: 'lbl_who_you_want_for_room_mate_seeing',
      desc: '',
      args: [],
    );
  }

  /// `What is your smoking preference?`
  String get lbl_What_is_your_smoking_preference {
    return Intl.message(
      'What is your smoking preference?',
      name: 'lbl_What_is_your_smoking_preference',
      desc: '',
      args: [],
    );
  }

  /// `What is your cleanliness level?`
  String get lbl_what_is_your_cleanliness_level {
    return Intl.message(
      'What is your cleanliness level?',
      name: 'lbl_what_is_your_cleanliness_level',
      desc: '',
      args: [],
    );
  }

  /// `Do you have any pets?`
  String get lbl_do_you_have_any_pets {
    return Intl.message(
      'Do you have any pets?',
      name: 'lbl_do_you_have_any_pets',
      desc: '',
      args: [],
    );
  }

  /// `What is your current class standing?`
  String get lbl_what_is_your_current_class_standing {
    return Intl.message(
      'What is your current class standing?',
      name: 'lbl_what_is_your_current_class_standing',
      desc: '',
      args: [],
    );
  }

  /// `Habits & Lifestyle`
  String get lbl_habits_lifestyle {
    return Intl.message(
      'Habits & Lifestyle',
      name: 'lbl_habits_lifestyle',
      desc: '',
      args: [],
    );
  }

  /// `Cleanliness & Living Style`
  String get lbl_cleanliness_living_style {
    return Intl.message(
      'Cleanliness & Living Style',
      name: 'lbl_cleanliness_living_style',
      desc: '',
      args: [],
    );
  }

  /// `Interests & Hobbies`
  String get lbl_interests_hobbies {
    return Intl.message(
      'Interests & Hobbies',
      name: 'lbl_interests_hobbies',
      desc: '',
      args: [],
    );
  }

  /// `Selected`
  String get lbl_selected {
    return Intl.message('Selected', name: 'lbl_selected', desc: '', args: []);
  }

  /// `Find the Right Room8`
  String get lbl_find_the_right_room8 {
    return Intl.message(
      'Find the Right Room8',
      name: 'lbl_find_the_right_room8',
      desc: '',
      args: [],
    );
  }

  /// `Tell us about your lifestyle - we’ll match you with people who vibe with it.`
  String get lbl_your_personal_details_desc3 {
    return Intl.message(
      'Tell us about your lifestyle - we’ll match you with people who vibe with it.',
      name: 'lbl_your_personal_details_desc3',
      desc: '',
      args: [],
    );
  }

  /// `Pick 5 Things:`
  String get lbl_pick_5_things {
    return Intl.message(
      'Pick 5 Things:',
      name: 'lbl_pick_5_things',
      desc: '',
      args: [],
    );
  }

  /// `That matter to you when living with a room8`
  String get lbl_that_matter_to_you_when_living_with_a_room8 {
    return Intl.message(
      'That matter to you when living with a room8',
      name: 'lbl_that_matter_to_you_when_living_with_a_room8',
      desc: '',
      args: [],
    );
  }

  /// `What are your interests?`
  String get lbl_what_are_your_interests {
    return Intl.message(
      'What are your interests?',
      name: 'lbl_what_are_your_interests',
      desc: '',
      args: [],
    );
  }

  /// `Settings`
  String get lbl_settings {
    return Intl.message('Settings', name: 'lbl_settings', desc: '', args: []);
  }

  /// `Privacy Policy`
  String get lbl_privacy_policy {
    return Intl.message(
      'Privacy Policy',
      name: 'lbl_privacy_policy',
      desc: '',
      args: [],
    );
  }

  /// `Notifications`
  String get lbl_notifications {
    return Intl.message(
      'Notifications',
      name: 'lbl_notifications',
      desc: '',
      args: [],
    );
  }

  /// `Terms & Condition`
  String get lbl_terms_condition {
    return Intl.message(
      'Terms & Condition',
      name: 'lbl_terms_condition',
      desc: '',
      args: [],
    );
  }

  /// `Blocked Accounts`
  String get lbl_blocked_accounts {
    return Intl.message(
      'Blocked Accounts',
      name: 'lbl_blocked_accounts',
      desc: '',
      args: [],
    );
  }

  /// `Get Help`
  String get lbl_get_help {
    return Intl.message('Get Help', name: 'lbl_get_help', desc: '', args: []);
  }

  /// `Log Out`
  String get lbl_log_out {
    return Intl.message('Log Out', name: 'lbl_log_out', desc: '', args: []);
  }

  /// `Lease Period`
  String get lbl_lease_period {
    return Intl.message(
      'Lease Period',
      name: 'lbl_lease_period',
      desc: '',
      args: [],
    );
  }

  /// `Photo Upload`
  String get lbl_photo_upload {
    return Intl.message(
      'Photo Upload',
      name: 'lbl_photo_upload',
      desc: '',
      args: [],
    );
  }

  /// `Save`
  String get lbl_save {
    return Intl.message('Save', name: 'lbl_save', desc: '', args: []);
  }

  /// `Move-Out`
  String get lbl_move_out {
    return Intl.message('Move-Out', name: 'lbl_move_out', desc: '', args: []);
  }

  /// `Move-In`
  String get lbl_move_in {
    return Intl.message('Move-In', name: 'lbl_move_in', desc: '', args: []);
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<Lang> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[Locale.fromSubtags(languageCode: 'en')];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<Lang> load(Locale locale) => Lang.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
