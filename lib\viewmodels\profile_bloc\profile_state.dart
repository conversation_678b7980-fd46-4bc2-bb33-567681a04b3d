part of 'profile_bloc.dart';

class ProfileState extends Equatable {
  final String phone;
  final TextEditingController phoneController;
  final FocusNode phoneFocusNode;
  final String leasePeriod;
  final TextEditingController leasePeriodController;
  final List<String> photoPaths;
  final String profileImagePath;
  final TextEditingController nameController;
  final TextEditingController emailController;
  final String gender;
  final TextEditingController ageController;
  final List<String> personalityTags;
  final List<String> customPersonalityTags;
  final GlobalKey<FormState> addPersonalDetailFormKey;
  final TextEditingController? fullNameController;
  final FocusNode? fullNameFocusNode;
  final TextEditingController? dobController;
  final TextEditingController? searchController;
  final FocusNode? dobFocusNode;
  final bool isloginLoading;
  final File? userProfile;
  final String? selectedGender;
  final String? selectedPeriod;
  final String? selectedSmokingPerson;
  final String? sleectedCleanLevenl;
  final String? selectedPet;
  final String? selectedClassStand;
  final List<String> selectedHabitsAndLifestyle;
  final List<String> selectedCleanlinessLivingStyle;
  final List<String> selectedInterestsHobbies;
  final bool isPickThings;
  final int selectedOption;

  const ProfileState({
    required this.phone,
    required this.phoneController,
    required this.phoneFocusNode,
    required this.leasePeriod,
    required this.leasePeriodController,
    required this.photoPaths,
    required this.profileImagePath,
    required this.nameController,
    required this.emailController,
    required this.gender,
    required this.ageController,
    required this.personalityTags,
    required this.customPersonalityTags,
    required this.addPersonalDetailFormKey,
    this.fullNameController,
    this.fullNameFocusNode,
    this.dobController,
    this.searchController,
    this.dobFocusNode,
    this.isloginLoading = false,
    this.userProfile,
    this.selectedGender,
    this.selectedPeriod = '9 months',
    this.selectedSmokingPerson,
    this.sleectedCleanLevenl,
    this.selectedPet,
    this.selectedClassStand,
    this.selectedHabitsAndLifestyle = const [],
    this.selectedCleanlinessLivingStyle = const [],
    this.selectedInterestsHobbies = const [],
    this.isPickThings = false,
    this.selectedOption = 0,
  });

  int get totalSelectedOptions =>
      selectedHabitsAndLifestyle.length +
      selectedCleanlinessLivingStyle.length +
      selectedInterestsHobbies.length;

  factory ProfileState.initial() {
    // Predefined tags as in the UI
    final predefinedTags = [
      'Friendly', 'Organized', 'Quiet', 'Outgoing', 'Clean', 'Adventurous',
      // Add more as needed
    ];
    // Default images for gallery
    final defaultPhotoPaths = [
      Assets.images.pngs.other.pngHomeUser.path,
      Assets.images.pngs.other.pngHomeUser.path,
      Assets.images.pngs.other.pngHomeUser.path,
      Assets.images.pngs.other.pngHomeUser.path,
    ];
    return ProfileState(
      phone: '',
      phoneController: TextEditingController(text: '1234567890'),
      phoneFocusNode: FocusNode(),
      leasePeriod: '600/ month',
      leasePeriodController: TextEditingController(text: '600/ month'),
      photoPaths: defaultPhotoPaths,
      profileImagePath: '',
      nameController: TextEditingController(text: 'Cameron Williamson'),
      emailController: TextEditingController(
        text: '<EMAIL>',
      ),
      gender: 'Male',
      ageController: TextEditingController(text: '20'),
      personalityTags: List<String>.from(predefinedTags),
      customPersonalityTags: const [],
      addPersonalDetailFormKey: GlobalKey<FormState>(),
      fullNameController: TextEditingController(),
      fullNameFocusNode: FocusNode(),
      dobController: TextEditingController(),
      dobFocusNode: FocusNode(),
    );
  }

  ProfileState copyWith({
    String? phone,
    TextEditingController? phoneController,
    FocusNode? phoneFocusNode,
    String? leasePeriod,
    TextEditingController? leasePeriodController,
    List<String>? photoPaths,
    String? profileImagePath,
    TextEditingController? nameController,
    TextEditingController? emailController,
    String? gender,
    TextEditingController? ageController,
    List<String>? personalityTags,
    List<String>? customPersonalityTags,

    GlobalKey<FormState>? addPersonalDetailFormKey,
    TextEditingController? fullNameController,
    FocusNode? fullNameFocusNode,
    TextEditingController? dobController,
    FocusNode? dobFocusNode,
    final TextEditingController? searchController,
    bool? isloginLoading,
    File? userProfile,
    final String? selectedGender,
    final String? selectedPeriod,
    final String? selectedSmokingPerson,
    final String? sleectedCleanLevenl,
    final String? selectedPet,
    final String? selectedClassStand,
    final List<String>? selectedHabitsAndLifestyle,
    final List<String>? selectedCleanlinessLivingStyle,
    final List<String>? selectedInterestsHobbies,
    final bool? isPickThings,
    final int? selectedOption,
  }) {
    return ProfileState(
      phone: phone ?? this.phone,
      phoneController: phoneController ?? this.phoneController,
      phoneFocusNode: phoneFocusNode ?? this.phoneFocusNode,
      leasePeriod: leasePeriod ?? this.leasePeriod,
      leasePeriodController:
          leasePeriodController ?? this.leasePeriodController,
      photoPaths: photoPaths ?? this.photoPaths,
      profileImagePath: profileImagePath ?? this.profileImagePath,
      nameController: nameController ?? this.nameController,
      emailController: emailController ?? this.emailController,
      gender: gender ?? this.gender,
      ageController: ageController ?? this.ageController,
      personalityTags: personalityTags ?? this.personalityTags,
      customPersonalityTags:
          customPersonalityTags ?? this.customPersonalityTags,

      addPersonalDetailFormKey:
          addPersonalDetailFormKey ?? this.addPersonalDetailFormKey,
      fullNameController: fullNameController ?? this.fullNameController,
      fullNameFocusNode: fullNameFocusNode ?? this.fullNameFocusNode,
      dobController: dobController ?? this.dobController,
      dobFocusNode: dobFocusNode ?? this.dobFocusNode,
      isloginLoading: isloginLoading ?? this.isloginLoading,
      userProfile: userProfile ?? this.userProfile,
      selectedGender: selectedGender ?? this.selectedGender,
      selectedPeriod: selectedPeriod ?? this.selectedPeriod,
      selectedSmokingPerson:
          selectedSmokingPerson ?? this.selectedSmokingPerson,
      sleectedCleanLevenl: sleectedCleanLevenl ?? this.sleectedCleanLevenl,
      selectedPet: selectedPet ?? this.selectedPet,
      selectedClassStand: selectedClassStand ?? this.selectedClassStand,
      selectedHabitsAndLifestyle:
          selectedHabitsAndLifestyle ?? this.selectedHabitsAndLifestyle,
      selectedCleanlinessLivingStyle:
          selectedCleanlinessLivingStyle ?? this.selectedCleanlinessLivingStyle,
      selectedInterestsHobbies:
          selectedInterestsHobbies ?? this.selectedInterestsHobbies,
      isPickThings: isPickThings ?? this.isPickThings,
      searchController: searchController ?? this.searchController,
      selectedOption: selectedOption ?? this.selectedOption,
    );
  }

  @override
  List<Object?> get props => [
    phone,
    phoneController,
    phoneFocusNode,
    leasePeriod,
    leasePeriodController,
    photoPaths,
    profileImagePath,
    nameController,
    emailController,
    gender,
    ageController,
    personalityTags,
    customPersonalityTags,
    addPersonalDetailFormKey,
    fullNameController,
    fullNameFocusNode,
    dobController,
    dobFocusNode,
    searchController,
    isloginLoading,
    userProfile,
    selectedGender,
    selectedPeriod,
    selectedSmokingPerson,
    sleectedCleanLevenl,
    selectedPet,
    selectedClassStand,
    selectedHabitsAndLifestyle,
    selectedCleanlinessLivingStyle,
    selectedInterestsHobbies,
    isPickThings,
    selectedOption,
  ];

  // @override
  // List<Object?> get props => [
  //   phone,
  //   phoneController,
  //   phoneFocusNode,
  //   leasePeriod,
  //   leasePeriodController,
  //   photoPaths,
  //   profileImagePath,
  //   nameController,
  //   emailController,
  //   gender,
  //   ageController,
  //   personalityTags,
  //   customPersonalityTags,
  //   addPersonalDetailFormKey,
  //   fullNameController,
  //   fullNameFocusNode,
  //   dobController,
  //   dobFocusNode,
  //   isloginLoading,
  // ];
}
